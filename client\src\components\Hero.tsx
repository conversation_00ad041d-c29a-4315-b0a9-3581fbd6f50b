import { Button } from "@/components/ui/button";

export default function <PERSON>() {
  return (
    <section className="relative gradient-hero text-white overflow-hidden">
      <div className="absolute inset-0 bg-black opacity-20"></div>
      <div 
        className="absolute inset-0 bg-cover bg-center opacity-30" 
        style={{
          backgroundImage: `url('https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1080')`
        }}
      ></div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                Master <span className="text-gradient-emerald">AI & Economics</span> Together
              </h1>
              <p className="text-xl lg:text-2xl text-gray-200 leading-relaxed">
                Join India's premier platform for learning the intersection of Artificial Intelligence and Economics. Expert-led webinars, comprehensive resources, and cutting-edge insights.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <Button className="gradient-emerald text-white px-8 py-4 rounded-xl text-lg font-semibold hover:opacity-90 transition-all transform hover:scale-105 shadow-lg">
                Start Learning Now
              </Button>
              <Button 
                variant="outline" 
                className="bg-white bg-opacity-20 backdrop-blur-sm text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-opacity-30 transition-all border border-white border-opacity-30"
              >
                Watch Demo
              </Button>
            </div>
            
            <div className="flex items-center gap-8 pt-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-emerald-400">5,000+</div>
                <div className="text-sm text-gray-300">Active Learners</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-emerald-400">50+</div>
                <div className="text-sm text-gray-300">Expert Webinars</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-emerald-400">4.9</div>
                <div className="text-sm text-gray-300">Average Rating</div>
              </div>
            </div>
          </div>
          
          <div className="lg:pl-8">
            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 shadow-2xl border border-white border-opacity-20">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Your Learning Dashboard</h3>
                  <div className="w-3 h-3 bg-emerald-400 rounded-full animate-pulse"></div>
                </div>
                
                <div className="space-y-4">
                  <div className="gradient-primary rounded-lg p-4">
                    <div className="text-sm text-white opacity-80">Current Course</div>
                    <div className="font-semibold">AI-Driven Market Analysis</div>
                    <div className="w-full bg-white bg-opacity-20 rounded-full h-2 mt-2">
                      <div className="bg-emerald-400 h-2 rounded-full w-3/4"></div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-white bg-opacity-10 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-emerald-400">12</div>
                      <div className="text-sm text-gray-300">Completed</div>
                    </div>
                    <div className="bg-white bg-opacity-10 rounded-lg p-4 text-center">
                      <div className="text-2xl font-bold text-emerald-400">8</div>
                      <div className="text-sm text-gray-300">In Progress</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
