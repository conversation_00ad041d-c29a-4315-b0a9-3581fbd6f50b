import { Button } from "@/components/ui/button";

export default function <PERSON>() {
  return (
    <section className="relative gradient-hero text-white overflow-hidden min-h-[90vh] flex items-center">
      <div className="absolute inset-0 bg-black/20"></div>
      <div
        className="absolute inset-0 bg-cover bg-center opacity-30"
        style={{
          backgroundImage: `url('https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1080')`
        }}
      ></div>

      <div className="relative container-responsive section-padding">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          <div className="space-y-6 lg:space-y-8 text-center lg:text-left">
            <div className="space-y-4 lg:space-y-6">
              <h1 className="text-responsive-2xl font-bold leading-tight">
                Master <span className="text-gradient-emerald">AI & Economics</span> Together
              </h1>
              <p className="text-lg sm:text-xl lg:text-2xl text-slate-200 leading-relaxed max-w-2xl mx-auto lg:mx-0">
                Join India's premier platform for learning the intersection of Artificial Intelligence and Economics. Expert-led webinars, comprehensive resources, and cutting-edge insights.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button className="gradient-emerald text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl text-base sm:text-lg font-semibold hover:opacity-90 transition-all transform hover:scale-105 shadow-lg">
                Start Learning Now
              </Button>
              <Button
                variant="outline"
                className="bg-white/20 backdrop-blur-sm text-white px-6 sm:px-8 py-3 sm:py-4 rounded-xl text-base sm:text-lg font-semibold hover:bg-white/30 transition-all border border-white/30"
              >
                Watch Demo
              </Button>
            </div>

            <div className="grid grid-cols-3 gap-4 sm:gap-6 lg:gap-8 pt-4 lg:pt-6 max-w-md mx-auto lg:mx-0">
              <div className="text-center">
                <div className="text-xl sm:text-2xl font-bold text-emerald-400">5,000+</div>
                <div className="text-xs sm:text-sm text-slate-300">Active Learners</div>
              </div>
              <div className="text-center">
                <div className="text-xl sm:text-2xl font-bold text-emerald-400">50+</div>
                <div className="text-xs sm:text-sm text-slate-300">Expert Webinars</div>
              </div>
              <div className="text-center">
                <div className="text-xl sm:text-2xl font-bold text-emerald-400">4.9</div>
                <div className="text-xs sm:text-sm text-slate-300">Average Rating</div>
              </div>
            </div>
          </div>
          
          <div className="lg:pl-8 mt-8 lg:mt-0">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 lg:p-8 shadow-2xl border border-white/20 max-w-md mx-auto lg:max-w-none">
              <div className="space-y-4 lg:space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-base lg:text-lg font-semibold">Your Learning Dashboard</h3>
                  <div className="w-3 h-3 bg-emerald-400 rounded-full animate-pulse"></div>
                </div>

                <div className="space-y-4">
                  <div className="gradient-primary rounded-lg p-4">
                    <div className="text-sm text-white/80">Current Course</div>
                    <div className="font-semibold text-sm lg:text-base">AI-Driven Market Analysis</div>
                    <div className="w-full bg-white/20 rounded-full h-2 mt-2">
                      <div className="bg-emerald-400 h-2 rounded-full w-3/4"></div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3 lg:gap-4">
                    <div className="bg-white/10 rounded-lg p-3 lg:p-4 text-center">
                      <div className="text-xl lg:text-2xl font-bold text-emerald-400">12</div>
                      <div className="text-xs lg:text-sm text-slate-300">Completed</div>
                    </div>
                    <div className="bg-white/10 rounded-lg p-3 lg:p-4 text-center">
                      <div className="text-xl lg:text-2xl font-bold text-purple-400">8</div>
                      <div className="text-xs lg:text-sm text-slate-300">In Progress</div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-emerald-400 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-sm font-bold">A</span>
                    </div>
                    <div className="min-w-0">
                      <div className="font-semibold text-sm">Next: Algorithmic Trading</div>
                      <div className="text-xs text-slate-300">Tomorrow, 7:00 PM IST</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
