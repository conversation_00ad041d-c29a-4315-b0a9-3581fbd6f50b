@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Base colors */
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(222, 84%, 4.9%);
  --muted: hsl(210, 40%, 98%);
  --muted-foreground: hsl(215, 13.8%, 34.1%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(222, 84%, 4.9%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(222, 84%, 4.9%);
  --border: hsl(214, 32%, 91%);
  --input: hsl(214, 32%, 91%);
  --primary: hsl(221, 83%, 53%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(210, 40%, 96%);
  --secondary-foreground: hsl(222, 84%, 4.9%);
  --accent: hsl(210, 40%, 96%);
  --accent-foreground: hsl(222, 84%, 4.9%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(221, 83%, 53%);
  --radius: 0.75rem;

  /* Enhanced Alnomics color palette */
  --primary-50: hsl(214, 100%, 97%);
  --primary-100: hsl(213, 94%, 93%);
  --primary-200: hsl(213, 97%, 87%);
  --primary-300: hsl(212, 96%, 78%);
  --primary-400: hsl(213, 94%, 68%);
  --primary-500: hsl(217, 91%, 60%);
  --primary-600: hsl(221, 83%, 53%);
  --primary-700: hsl(224, 76%, 48%);
  --primary-800: hsl(226, 71%, 40%);
  --primary-900: hsl(224, 64%, 33%);
  --primary-950: hsl(226, 55%, 20%);

  /* Purple accent colors */
  --purple-50: hsl(270, 100%, 98%);
  --purple-100: hsl(269, 100%, 95%);
  --purple-200: hsl(269, 100%, 92%);
  --purple-300: hsl(269, 100%, 86%);
  --purple-400: hsl(270, 95%, 75%);
  --purple-500: hsl(271, 91%, 65%);
  --purple-600: hsl(271, 81%, 56%);
  --purple-700: hsl(272, 72%, 47%);
  --purple-800: hsl(272, 67%, 39%);
  --purple-900: hsl(273, 61%, 32%);
  --purple-950: hsl(274, 66%, 21%);

  /* Emerald success colors */
  --emerald-50: hsl(151, 81%, 96%);
  --emerald-100: hsl(149, 80%, 90%);
  --emerald-200: hsl(152, 76%, 80%);
  --emerald-300: hsl(156, 72%, 67%);
  --emerald-400: hsl(158, 64%, 52%);
  --emerald-500: hsl(160, 84%, 39%);
  --emerald-600: hsl(161, 94%, 30%);
  --emerald-700: hsl(163, 94%, 24%);
  --emerald-800: hsl(163, 88%, 20%);
  --emerald-900: hsl(164, 86%, 16%);
  --emerald-950: hsl(166, 91%, 9%);

  /* Neutral grays */
  --slate-50: hsl(210, 40%, 98%);
  --slate-100: hsl(210, 40%, 96%);
  --slate-200: hsl(214, 32%, 91%);
  --slate-300: hsl(213, 27%, 84%);
  --slate-400: hsl(215, 20%, 65%);
  --slate-500: hsl(215, 16%, 47%);
  --slate-600: hsl(215, 19%, 35%);
  --slate-700: hsl(215, 25%, 27%);
  --slate-800: hsl(217, 33%, 17%);
  --slate-900: hsl(222, 84%, 4.9%);
  --slate-950: hsl(229, 84%, 5%);
}

.dark {
  --background: hsl(224, 71.4%, 4.1%);
  --foreground: hsl(210, 40%, 98%);
  --muted: hsl(215, 27.9%, 16.9%);
  --muted-foreground: hsl(217, 10.6%, 64.9%);
  --popover: hsl(224, 71.4%, 4.1%);
  --popover-foreground: hsl(210, 40%, 98%);
  --card: hsl(224, 71.4%, 4.1%);
  --card-foreground: hsl(210, 40%, 98%);
  --border: hsl(215, 27.9%, 16.9%);
  --input: hsl(215, 27.9%, 16.9%);
  --primary: hsl(217, 91%, 60%);
  --primary-foreground: hsl(222, 84%, 4.9%);
  --secondary: hsl(215, 27.9%, 16.9%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --accent: hsl(215, 27.9%, 16.9%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(217, 91%, 60%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }
}

@layer utilities {
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary-600)), hsl(var(--purple-600)));
  }
  
  .gradient-hero {
    background: linear-gradient(135deg, hsl(var(--primary-900)), hsl(var(--primary-800)), hsl(var(--purple-800)));
  }

  .gradient-subtle {
    background: linear-gradient(135deg, hsl(var(--primary-50)), hsl(var(--purple-50)));
  }
  
  .gradient-emerald {
    background: linear-gradient(135deg, hsl(var(--emerald-500)), hsl(var(--emerald-600)));
  }
  
  .text-gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary-600)), hsl(var(--purple-600)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-gradient-emerald {
    background: linear-gradient(135deg, hsl(var(--emerald-500)), hsl(var(--emerald-600)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Responsive utilities */
  .container-responsive {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .section-padding {
    @apply py-12 sm:py-16 lg:py-20;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }

  .text-responsive-xl {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  .text-responsive-2xl {
    @apply text-3xl sm:text-4xl lg:text-5xl xl:text-6xl;
  }

  .grid-responsive-cards {
    @apply grid gap-6 sm:gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  .grid-responsive-pricing {
    @apply grid gap-6 sm:gap-8 grid-cols-1 lg:grid-cols-2;
  }

  /* Text truncation utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Enhanced focus states */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-background;
  }

  /* Smooth animations */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(40px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Improved button hover states */
  .btn-hover-lift {
    @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 active:shadow-md;
  }

  /* Accessibility improvements */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .card-hover,
    .btn-hover-lift,
    .animate-fade-in,
    .animate-slide-up {
      animation: none;
      transition: none;
      transform: none;
    }
  }
}
