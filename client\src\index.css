@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(224, 71.4%, 4.1%);
  --muted: hsl(220, 14.3%, 95.9%);
  --muted-foreground: hsl(220, 8.9%, 46.1%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(224, 71.4%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(224, 71.4%, 4.1%);
  --border: hsl(220, 13%, 91%);
  --input: hsl(220, 13%, 91%);
  --primary: hsl(221, 83%, 53%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(220, 14.3%, 95.9%);
  --secondary-foreground: hsl(220, 9%, 46.1%);
  --accent: hsl(220, 14.3%, 95.9%);
  --accent-foreground: hsl(220, 9%, 46.1%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(221, 83%, 53%);
  --radius: 0.5rem;
  
  /* Custom Alnomics colors */
  --primary-50: hsl(214, 100%, 97%);
  --primary-100: hsl(213, 94%, 93%);
  --primary-500: hsl(217, 91%, 60%);
  --primary-600: hsl(221, 83%, 53%);
  --primary-700: hsl(224, 76%, 48%);
  --primary-800: hsl(226, 71%, 40%);
  --primary-900: hsl(224, 64%, 33%);
  
  --purple-500: hsl(271, 91%, 65%);
  --purple-600: hsl(271, 81%, 56%);
  --purple-700: hsl(272, 72%, 47%);
  
  --emerald-500: hsl(160, 84%, 39%);
  --emerald-600: hsl(158, 64%, 52%);
  
  --slate-50: hsl(210, 40%, 98%);
}

.dark {
  --background: hsl(224, 71.4%, 4.1%);
  --foreground: hsl(210, 40%, 98%);
  --muted: hsl(215, 27.9%, 16.9%);
  --muted-foreground: hsl(217, 10.6%, 64.9%);
  --popover: hsl(224, 71.4%, 4.1%);
  --popover-foreground: hsl(210, 40%, 98%);
  --card: hsl(224, 71.4%, 4.1%);
  --card-foreground: hsl(210, 40%, 98%);
  --border: hsl(215, 27.9%, 16.9%);
  --input: hsl(215, 27.9%, 16.9%);
  --primary: hsl(217, 91%, 60%);
  --primary-foreground: hsl(222, 84%, 4.9%);
  --secondary: hsl(215, 27.9%, 16.9%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --accent: hsl(215, 27.9%, 16.9%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(217, 91%, 60%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }
}

@layer utilities {
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary-600)), hsl(var(--purple-600)));
  }
  
  .gradient-hero {
    background: linear-gradient(135deg, hsl(var(--primary-900)), hsl(var(--primary-800)), hsl(var(--purple-900)));
  }
  
  .gradient-emerald {
    background: linear-gradient(135deg, hsl(var(--emerald-500)), hsl(var(--emerald-600)));
  }
  
  .text-gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary-600)), hsl(var(--purple-600)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-gradient-emerald {
    background: linear-gradient(135deg, hsl(var(--emerald-500)), hsl(var(--emerald-600)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}
