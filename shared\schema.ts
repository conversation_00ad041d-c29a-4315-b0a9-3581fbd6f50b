import { pgTable, text, serial, integer, boolean, timestamp, decimal } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";

// Users table
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  firstName: text("first_name"),
  lastName: text("last_name"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Subscriptions table
export const subscriptions = pgTable("subscriptions", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  planType: text("plan_type").notNull(), // 'monthly' or 'yearly'
  status: text("status").notNull(), // 'active', 'cancelled', 'expired'
  startDate: timestamp("start_date").defaultNow().notNull(),
  endDate: timestamp("end_date").notNull(),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Webinars table
export const webinars = pgTable("webinars", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  instructor: text("instructor").notNull(),
  instructorRole: text("instructor_role").notNull(),
  date: timestamp("date").notNull(),
  duration: integer("duration_minutes").notNull(), // in minutes
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  status: text("status").notNull(), // 'upcoming', 'live', 'recorded'
  imageUrl: text("image_url"),
  avatarUrl: text("avatar_url"),
  viewCount: integer("view_count").default(0),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

// Webinar registrations table
export const webinarRegistrations = pgTable("webinar_registrations", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  webinarId: integer("webinar_id").references(() => webinars.id).notNull(),
  registeredAt: timestamp("registered_at").defaultNow().notNull(),
  attended: boolean("attended").default(false),
});

// eBook access table
export const ebookAccess = pgTable("ebook_access", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  purchasedAt: timestamp("purchased_at").defaultNow().notNull(),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(),
  accessExpiresAt: timestamp("access_expires_at"), // null for lifetime access
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  subscriptions: many(subscriptions),
  webinarRegistrations: many(webinarRegistrations),
  ebookAccess: many(ebookAccess),
}));

export const subscriptionsRelations = relations(subscriptions, ({ one }) => ({
  user: one(users, {
    fields: [subscriptions.userId],
    references: [users.id],
  }),
}));

export const webinarsRelations = relations(webinars, ({ many }) => ({
  registrations: many(webinarRegistrations),
}));

export const webinarRegistrationsRelations = relations(webinarRegistrations, ({ one }) => ({
  user: one(users, {
    fields: [webinarRegistrations.userId],
    references: [users.id],
  }),
  webinar: one(webinars, {
    fields: [webinarRegistrations.webinarId],
    references: [webinars.id],
  }),
}));

export const ebookAccessRelations = relations(ebookAccess, ({ one }) => ({
  user: one(users, {
    fields: [ebookAccess.userId],
    references: [users.id],
  }),
}));

// Schemas for inserts
export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  email: true,
  password: true,
  firstName: true,
  lastName: true,
});

export const insertSubscriptionSchema = createInsertSchema(subscriptions).pick({
  userId: true,
  planType: true,
  status: true,
  endDate: true,
  price: true,
});

export const insertWebinarSchema = createInsertSchema(webinars).pick({
  title: true,
  description: true,
  instructor: true,
  instructorRole: true,
  date: true,
  duration: true,
  price: true,
  status: true,
  imageUrl: true,
  avatarUrl: true,
});

export const insertWebinarRegistrationSchema = createInsertSchema(webinarRegistrations).pick({
  userId: true,
  webinarId: true,
});

export const insertEbookAccessSchema = createInsertSchema(ebookAccess).pick({
  userId: true,
  price: true,
  accessExpiresAt: true,
});

// Types
export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type InsertSubscription = z.infer<typeof insertSubscriptionSchema>;
export type Subscription = typeof subscriptions.$inferSelect;
export type InsertWebinar = z.infer<typeof insertWebinarSchema>;
export type Webinar = typeof webinars.$inferSelect;
export type InsertWebinarRegistration = z.infer<typeof insertWebinarRegistrationSchema>;
export type WebinarRegistration = typeof webinarRegistrations.$inferSelect;
export type InsertEbookAccess = z.infer<typeof insertEbookAccessSchema>;
export type EbookAccess = typeof ebookAccess.$inferSelect;
