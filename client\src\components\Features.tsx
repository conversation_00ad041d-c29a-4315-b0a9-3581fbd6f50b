import { Card, CardContent } from "@/components/ui/card";
import { Brain, Users, TrendingUp, Award, Smartphone, Headphones, Check } from "lucide-react";

const features = [
  {
    icon: Brain,
    title: "AI Learning Tools",
    description: "Interactive simulations and AI-powered learning assistants to help you understand complex economic models.",
    color: "from-blue-500 to-blue-600",
    items: ["Neural network simulators", "Economic model builders", "Data visualization tools"]
  },
  {
    icon: Users,
    title: "Expert Community", 
    description: "Connect with fellow learners, industry experts, and get your questions answered in real-time.",
    color: "from-purple-500 to-purple-600",
    items: ["Discussion forums", "Study groups", "Networking events"]
  },
  {
    icon: TrendingUp,
    title: "Progress Tracking",
    description: "Monitor your learning journey with detailed analytics and personalized recommendations.",
    color: "from-emerald-500 to-emerald-600", 
    items: ["Learning analytics", "Skill assessments", "Achievement badges"]
  },
  {
    icon: Award,
    title: "Certifications",
    description: "Earn industry-recognized certificates to validate your AI and Economics expertise.",
    color: "from-orange-500 to-red-500",
    items: ["Course completion certificates", "Skill-based badges", "LinkedIn integration"]
  },
  {
    icon: Smartphone,
    title: "Mobile Learning",
    description: "Learn on-the-go with our mobile app, featuring offline content and synchronized progress.",
    color: "from-blue-500 to-indigo-600",
    items: ["iOS & Android apps", "Offline content access", "Push notifications"]
  },
  {
    icon: Headphones,
    title: "Expert Support",
    description: "Get personalized help from our team of AI and Economics experts whenever you need it.",
    color: "from-pink-500 to-rose-500",
    items: ["24/7 chat support", "Weekly office hours", "1-on-1 mentoring"]
  }
];

export default function Features() {
  return (
    <section id="features" className="py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Everything You Need to Excel</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Comprehensive tools and resources designed to accelerate your AI and Economics mastery.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card 
              key={index}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              <CardContent className="p-0">
                <div className={`bg-gradient-to-br ${feature.color} w-16 h-16 rounded-2xl flex items-center justify-center mb-6`}>
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h3>
                <p className="text-gray-600 mb-6">{feature.description}</p>
                
                <ul className="text-sm text-gray-600 space-y-2">
                  {feature.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-center gap-2">
                      <Check className="w-3 h-3 text-emerald-500" />
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
