import { Card, CardContent } from "@/components/ui/card";
import { Brain, Users, TrendingUp, Award, Smartphone, Headphones, Check } from "lucide-react";

const features = [
  {
    icon: Brain,
    title: "AI Learning Tools",
    description: "Interactive simulations and AI-powered learning assistants to help you understand complex economic models.",
    color: "from-blue-500 to-blue-600",
    items: ["Neural network simulators", "Economic model builders", "Data visualization tools"]
  },
  {
    icon: Users,
    title: "Expert Community", 
    description: "Connect with fellow learners, industry experts, and get your questions answered in real-time.",
    color: "from-purple-500 to-purple-600",
    items: ["Discussion forums", "Study groups", "Networking events"]
  },
  {
    icon: TrendingUp,
    title: "Progress Tracking",
    description: "Monitor your learning journey with detailed analytics and personalized recommendations.",
    color: "from-emerald-500 to-emerald-600", 
    items: ["Learning analytics", "Skill assessments", "Achievement badges"]
  },
  {
    icon: Award,
    title: "Certifications",
    description: "Earn industry-recognized certificates to validate your AI and Economics expertise.",
    color: "from-orange-500 to-red-500",
    items: ["Course completion certificates", "Skill-based badges", "LinkedIn integration"]
  },
  {
    icon: Smartphone,
    title: "Mobile Learning",
    description: "Learn on-the-go with our mobile app, featuring offline content and synchronized progress.",
    color: "from-blue-500 to-indigo-600",
    items: ["iOS & Android apps", "Offline content access", "Push notifications"]
  },
  {
    icon: Headphones,
    title: "Expert Support",
    description: "Get personalized help from our team of AI and Economics experts whenever you need it.",
    color: "from-pink-500 to-rose-500",
    items: ["24/7 chat support", "Weekly office hours", "1-on-1 mentoring"]
  }
];

export default function Features() {
  return (
    <section id="features" className="section-padding gradient-subtle">
      <div className="container-responsive">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-responsive-xl font-bold text-slate-900 mb-4">Everything You Need to Excel</h2>
          <p className="text-lg lg:text-xl text-slate-600 max-w-3xl mx-auto">
            Comprehensive tools and resources designed to accelerate your AI and Economics mastery.
          </p>
        </div>

        <div className="grid-responsive-cards">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 lg:p-8 shadow-lg card-hover"
            >
              <CardContent className="p-0">
                <div className={`bg-gradient-to-br ${feature.color} w-12 lg:w-16 h-12 lg:h-16 rounded-2xl flex items-center justify-center mb-4 lg:mb-6`}>
                  <feature.icon className="w-6 lg:w-8 h-6 lg:h-8 text-white" />
                </div>

                <h3 className="text-lg lg:text-xl font-bold text-slate-900 mb-3 lg:mb-4">{feature.title}</h3>
                <p className="text-slate-600 mb-4 lg:mb-6 text-sm lg:text-base">{feature.description}</p>

                <ul className="text-xs lg:text-sm text-slate-600 space-y-2">
                  {feature.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-center gap-2">
                      <Check className="w-3 h-3 text-emerald-500 flex-shrink-0" />
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
