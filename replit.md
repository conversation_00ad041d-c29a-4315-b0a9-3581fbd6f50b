# Alnomics - AI & Economics Learning Platform

## Overview

Alnomics is a modern educational technology platform focused on the intersection of Artificial Intelligence and Economics. The application provides expert-led webinars, comprehensive eBooks, and subscription-based learning plans. Built as a full-stack web application with a React frontend and Express backend, it features a sophisticated landing page showcasing courses, pricing plans, and educational resources.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Routing**: Wouter (lightweight React router)
- **State Management**: TanStack Query (React Query) for server state
- **UI Framework**: Shadcn/ui components with Radix UI primitives
- **Styling**: Tailwind CSS with custom design system
- **Build Tool**: Vite for development and bundling

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ES modules
- **Database ORM**: Drizzle ORM
- **Database**: PostgreSQL (configured for Neon Database)
- **Session Management**: Express sessions with PostgreSQL store
- **Development**: Hot reload with Vite integration

### Design System
- **Color Scheme**: Blue and purple gradients with emerald accents
- **Typography**: Inter font family
- **Theme**: Light mode with CSS custom properties
- **Components**: Consistent component library with variants

## Key Components

### Frontend Components
1. **Landing Page Sections**:
   - Header with navigation
   - Hero section with call-to-action
   - Webinars showcase
   - eBook preview and features
   - Pricing plans comparison
   - Features overview
   - Footer with links and social media

2. **UI Components**:
   - Comprehensive Shadcn/ui component library
   - Custom styled buttons, cards, and forms
   - Responsive navigation and layout components
   - Toast notifications and tooltips

### Backend Components
1. **Server Structure**:
   - Express application with middleware setup
   - Route registration system
   - Error handling middleware
   - Development/production environment configuration

2. **Storage Layer**:
   - In-memory storage implementation for development
   - User management interface
   - Database schema definitions with Drizzle

## Data Flow

### Client-Server Communication
- RESTful API endpoints (prefixed with `/api`)
- JSON request/response format
- TanStack Query for data fetching and caching
- Error handling with custom error boundaries

### Database Schema
- **Users Table**: Basic user authentication structure
- **Session Management**: PostgreSQL-backed sessions
- **Migration System**: Drizzle migrations in `/migrations` directory

## External Dependencies

### Production Dependencies
- **UI Libraries**: Radix UI primitives, Lucide React icons
- **Database**: Neon Database serverless PostgreSQL
- **Forms**: React Hook Form with Zod validation
- **Utilities**: Date-fns, class-variance-authority, clsx

### Development Dependencies
- **Build Tools**: Vite, ESBuild, TypeScript
- **Code Quality**: PostCSS, Autoprefixer
- **Development**: TSX for TypeScript execution, Replit integration

## Deployment Strategy

### Build Process
1. **Frontend**: Vite builds React application to `dist/public`
2. **Backend**: ESBuild bundles server code to `dist/index.js`
3. **Database**: Drizzle migrations applied via `db:push` command

### Environment Configuration
- **Development**: Local development with hot reload
- **Production**: Node.js server serving static files and API
- **Database**: Environment variable-based configuration (`DATABASE_URL`)

### Scripts
- `dev`: Development server with hot reload
- `build`: Production build for both frontend and backend
- `start`: Production server execution
- `db:push`: Database migration deployment

## Changelog
- July 02, 2025: Database integration completed
  - Added PostgreSQL database with comprehensive schema
  - Implemented DatabaseStorage replacing MemStorage
  - Created API endpoints for webinars, users, subscriptions, and eBook access
  - Updated frontend to fetch real data from database
  - Seeded database with sample webinar data
- July 02, 2025: Initial setup

## User Preferences

Preferred communication style: Simple, everyday language.