import { db } from "./db";
import { webinars } from "@shared/schema";

const sampleWebinars = [
  {
    title: "AI-Powered Economic Forecasting",
    description: "Learn how machine learning algorithms are reshaping economic predictions and market analysis.",
    instructor: "Dr<PERSON>",
    instructorRole: "AI Economics Expert",
    date: new Date("2024-12-28T19:00:00.000Z"), // 7:00 PM IST
    duration: 120, // 2 hours in minutes
    price: "199.00",
    status: "upcoming" as const,
    imageUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",
    avatarUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100",
    viewCount: 0,
  },
  {
    title: "Behavioral Economics & AI",
    description: "Explore how AI models can predict and analyze human economic behavior patterns.",
    instructor: "Prof. <PERSON>",
    instructorRole: "Behavioral Economist",
    date: new Date("2025-01-05T18:30:00.000Z"), // 6:30 PM IST
    duration: 150, // 2.5 hours in minutes
    price: "199.00",
    status: "upcoming" as const,
    imageUrl: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",
    avatarUrl: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100",
    viewCount: 0,
  },
  {
    title: "Crypto Economics & AI Analysis",
    description: "Understand how AI tools are revolutionizing cryptocurrency market analysis and DeFi economics.",
    instructor: "Dr. Vikram Singh",
    instructorRole: "Crypto Economist",
    date: new Date("2024-12-15T18:00:00.000Z"), // Past date for recorded webinar
    duration: 150, // 2.5 hours in minutes
    price: "199.00",
    status: "recorded" as const,
    imageUrl: "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",
    avatarUrl: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100",
    viewCount: 1247,
  }
];

export async function seedWebinars() {
  try {
    console.log("Seeding webinars...");
    
    // Clear existing webinars first
    await db.delete(webinars);
    
    // Insert sample webinars
    await db.insert(webinars).values(sampleWebinars);
    
    console.log("Webinars seeded successfully!");
  } catch (error) {
    console.error("Error seeding webinars:", error);
    throw error;
  }
}

// Run the seeding if this file is executed directly
seedWebinars()
  .then(() => {
    console.log("Database seeding completed!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("Database seeding failed:", error);
    process.exit(1);
  });