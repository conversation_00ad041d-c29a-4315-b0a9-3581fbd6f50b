import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Shield, Infinity, RefreshCw, Check, Bookmark, Highlighter, Clock } from "lucide-react";

const features = [
  {
    icon: Shield,
    title: "Secure Access",
    description: "Protected viewing with no download options - content stays secure"
  },
  {
    icon: Infinity,
    title: "Unlimited Access", 
    description: "Read anytime, anywhere with your account login"
  },
  {
    icon: RefreshCw,
    title: "Regular Updates",
    description: "Content updated monthly with latest AI economics trends"
  }
];

const contents = [
  "AI Market Models",
  "Economic Algorithms", 
  "Case Studies",
  "Future Predictions",
  "Implementation Guides",
  "Expert Interviews"
];

export default function EBook() {
  return (
    <section id="ebook" className="section-padding gradient-subtle">
      <div className="container-responsive">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          <div className="space-y-6 lg:space-y-8 text-center lg:text-left">
            <div className="space-y-4 lg:space-y-6">
              <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-3 mb-4">
                <div className="gradient-emerald text-white px-3 lg:px-4 py-1.5 lg:py-2 rounded-full text-xs lg:text-sm font-semibold flex items-center gap-2">
                  <Shield className="w-3 lg:w-4 h-3 lg:h-4" />
                  Protected Content
                </div>
                <div className="text-2xl lg:text-3xl font-bold text-primary-600">₹99</div>
              </div>

              <h2 className="text-responsive-xl font-bold text-slate-900">
                The Complete Guide to <span className="text-gradient-primary">AI Economics</span>
              </h2>

              <p className="text-lg lg:text-xl text-slate-600 leading-relaxed">
                Access our comprehensive 300+ page eBook covering the intersection of AI and Economics. Secure, non-downloadable format ensures content protection while providing unlimited online access.
              </p>
            </div>

            <div className="space-y-3 lg:space-y-4">
              {features.map((feature, index) => (
                <div key={index} className="flex items-start gap-3 lg:gap-4">
                  <div className="bg-primary-100 p-2 rounded-lg flex-shrink-0">
                    <feature.icon className="w-4 lg:w-5 h-4 lg:h-5 text-primary-600" />
                  </div>
                  <div className="min-w-0">
                    <h4 className="font-semibold text-slate-900 text-sm lg:text-base">{feature.title}</h4>
                    <p className="text-slate-600 text-sm lg:text-base">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>

            <Card className="bg-white/80 backdrop-blur-sm">
              <CardContent className="p-4 lg:p-6">
                <h4 className="font-semibold text-slate-900 mb-3 lg:mb-4 text-sm lg:text-base">What's Inside:</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 lg:gap-4 text-xs lg:text-sm">
                  {contents.map((item, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Check className="w-3 lg:w-4 h-3 lg:h-4 text-emerald-500 flex-shrink-0" />
                      <span>{item}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Button className="w-full sm:w-auto gradient-emerald text-white px-6 lg:px-8 py-3 lg:py-4 rounded-xl text-base lg:text-lg font-semibold hover:opacity-90 transition-all transform hover:scale-105 shadow-lg">
              Get Secure Access - ₹99
            </Button>
          </div>

          <div className="relative mt-8 lg:mt-0">
            <div className="bg-white rounded-3xl shadow-2xl p-6 lg:p-8 transform rotate-1 lg:rotate-3 hover:rotate-0 transition-all duration-500 max-w-md mx-auto lg:max-w-none">
              <div className="gradient-hero rounded-2xl p-4 lg:p-6 text-white">
                <div className="flex items-center justify-between mb-4 lg:mb-6">
                  <h3 className="text-lg lg:text-xl font-bold">AI Economics Guide</h3>
                  <div className="bg-white/20 px-2 lg:px-3 py-1 rounded-full text-xs lg:text-sm flex items-center gap-1">
                    <Shield className="w-3 h-3" />
                    Protected
                  </div>
                </div>

                <div className="space-y-3 lg:space-y-4 text-xs lg:text-sm leading-relaxed">
                  <p className="border-l-4 border-emerald-400 pl-3 lg:pl-4">
                    "Chapter 3: Machine Learning in Economic Modeling"
                  </p>
                  <p className="text-white/80">
                    The integration of machine learning algorithms into traditional economic models has revolutionized how we approach market prediction and analysis...
                  </p>
                  <div className="flex items-center justify-between pt-3 lg:pt-4 border-t border-white/20">
                    <span className="text-xs lg:text-sm">Page 47 of 312</span>
                    <div className="flex gap-1 lg:gap-2">
                      <div className="w-1.5 lg:w-2 h-1.5 lg:h-2 bg-emerald-400 rounded-full"></div>
                      <div className="w-1.5 lg:w-2 h-1.5 lg:h-2 bg-white/40 rounded-full"></div>
                      <div className="w-1.5 lg:w-2 h-1.5 lg:h-2 bg-white/40 rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-4 lg:mt-6 grid grid-cols-3 gap-2 lg:gap-4">
                <div className="bg-slate-100 rounded-lg p-2 lg:p-3 text-center">
                  <Bookmark className="w-4 lg:w-5 h-4 lg:h-5 text-primary-600 mb-1 lg:mb-2 mx-auto" />
                  <div className="text-xs text-slate-600">15 Bookmarks</div>
                </div>
                <div className="bg-slate-100 rounded-lg p-2 lg:p-3 text-center">
                  <Highlighter className="w-4 lg:w-5 h-4 lg:h-5 text-emerald-500 mb-1 lg:mb-2 mx-auto" />
                  <div className="text-xs text-slate-600">8 Highlights</div>
                </div>
                <div className="bg-slate-100 rounded-lg p-2 lg:p-3 text-center">
                  <Clock className="w-4 lg:w-5 h-4 lg:h-5 text-purple-600 mb-1 lg:mb-2 mx-auto" />
                  <div className="text-xs text-slate-600">2h 15m</div>
                </div>
              </div>
            </div>

            <div className="absolute -bottom-3 lg:-bottom-4 -right-3 lg:-right-4 bg-emerald-500 text-white p-3 lg:p-4 rounded-full shadow-lg">
              <Shield className="w-5 lg:w-6 h-5 lg:h-6" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
