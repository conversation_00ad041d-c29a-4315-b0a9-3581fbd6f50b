import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Shield, Infinity, RefreshCw, Check, Bookmark, Highlighter, Clock } from "lucide-react";

const features = [
  {
    icon: Shield,
    title: "Secure Access",
    description: "Protected viewing with no download options - content stays secure"
  },
  {
    icon: Infinity,
    title: "Unlimited Access", 
    description: "Read anytime, anywhere with your account login"
  },
  {
    icon: RefreshCw,
    title: "Regular Updates",
    description: "Content updated monthly with latest AI economics trends"
  }
];

const contents = [
  "AI Market Models",
  "Economic Algorithms", 
  "Case Studies",
  "Future Predictions",
  "Implementation Guides",
  "Expert Interviews"
];

export default function EBook() {
  return (
    <section id="ebook" className="py-20 bg-gradient-to-r from-slate-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <div className="space-y-4">
              <div className="flex items-center gap-3 mb-4">
                <div className="gradient-emerald text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center gap-2">
                  <Shield className="w-4 h-4" />
                  Protected Content
                </div>
                <div className="text-3xl font-bold text-blue-600">₹99</div>
              </div>
              
              <h2 className="text-4xl font-bold text-gray-900">
                The Complete Guide to <span className="text-gradient-primary">AI Economics</span>
              </h2>
              
              <p className="text-xl text-gray-600 leading-relaxed">
                Access our comprehensive 300+ page eBook covering the intersection of AI and Economics. Secure, non-downloadable format ensures content protection while providing unlimited online access.
              </p>
            </div>
            
            <div className="space-y-4">
              {features.map((feature, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className="bg-blue-100 p-2 rounded-lg">
                    <feature.icon className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{feature.title}</h4>
                    <p className="text-gray-600">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
            
            <Card>
              <CardContent className="p-6">
                <h4 className="font-semibold text-gray-900 mb-4">What's Inside:</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  {contents.map((item, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Check className="w-4 h-4 text-emerald-500" />
                      <span>{item}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
            
            <Button className="gradient-emerald text-white px-8 py-4 rounded-xl text-lg font-semibold hover:opacity-90 transition-all transform hover:scale-105 shadow-lg">
              Get Secure Access - ₹99
            </Button>
          </div>
          
          <div className="relative">
            <div className="bg-white rounded-3xl shadow-2xl p-8 transform rotate-3 hover:rotate-0 transition-all duration-500">
              <div className="gradient-hero rounded-2xl p-6 text-white">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold">AI Economics Guide</h3>
                  <div className="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm flex items-center gap-1">
                    <Shield className="w-3 h-3" />
                    Protected
                  </div>
                </div>
                
                <div className="space-y-4 text-sm leading-relaxed">
                  <p className="border-l-4 border-emerald-400 pl-4">
                    "Chapter 3: Machine Learning in Economic Modeling"
                  </p>
                  <p className="opacity-80">
                    The integration of machine learning algorithms into traditional economic models has revolutionized how we approach market prediction and analysis...
                  </p>
                  <div className="flex items-center justify-between pt-4 border-t border-white border-opacity-20">
                    <span>Page 47 of 312</span>
                    <div className="flex gap-2">
                      <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                      <div className="w-2 h-2 bg-white bg-opacity-40 rounded-full"></div>
                      <div className="w-2 h-2 bg-white bg-opacity-40 rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 grid grid-cols-3 gap-4">
                <div className="bg-gray-100 rounded-lg p-3 text-center">
                  <Bookmark className="w-5 h-5 text-blue-600 mb-2 mx-auto" />
                  <div className="text-xs text-gray-600">15 Bookmarks</div>
                </div>
                <div className="bg-gray-100 rounded-lg p-3 text-center">
                  <Highlighter className="w-5 h-5 text-emerald-500 mb-2 mx-auto" />
                  <div className="text-xs text-gray-600">8 Highlights</div>
                </div>
                <div className="bg-gray-100 rounded-lg p-3 text-center">
                  <Clock className="w-5 h-5 text-purple-600 mb-2 mx-auto" />
                  <div className="text-xs text-gray-600">2h 15m</div>
                </div>
              </div>
            </div>
            
            <div className="absolute -bottom-4 -right-4 bg-emerald-500 text-white p-4 rounded-full shadow-lg">
              <Shield className="w-6 h-6" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
