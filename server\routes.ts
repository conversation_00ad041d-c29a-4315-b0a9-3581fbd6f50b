import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertUserSchema, insertWebinarRegistrationSchema, insertEbookAccessSchema, insertSubscriptionSchema } from "@shared/schema";

export async function registerRoutes(app: Express): Promise<Server> {
  
  // Get all webinars
  app.get("/api/webinars", async (req, res) => {
    try {
      const webinars = await storage.getAllWebinars();
      res.json(webinars);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch webinars" });
    }
  });

  // Get a specific webinar
  app.get("/api/webinars/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const webinar = await storage.getWebinar(id);
      if (!webinar) {
        return res.status(404).json({ error: "Webinar not found" });
      }
      res.json(webinar);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch webinar" });
    }
  });

  // Create a new user
  app.post("/api/users", async (req, res) => {
    try {
      const userData = insertUserSchema.parse(req.body);
      const existingUser = await storage.getUserByUsername(userData.username);
      if (existingUser) {
        return res.status(400).json({ error: "Username already exists" });
      }
      
      const existingEmail = await storage.getUserByEmail(userData.email);
      if (existingEmail) {
        return res.status(400).json({ error: "Email already exists" });
      }

      const user = await storage.createUser(userData);
      const { password, ...userWithoutPassword } = user;
      res.status(201).json(userWithoutPassword);
    } catch (error) {
      res.status(400).json({ error: "Invalid user data" });
    }
  });

  // Register for a webinar
  app.post("/api/webinar-registrations", async (req, res) => {
    try {
      const registrationData = insertWebinarRegistrationSchema.parse(req.body);
      
      // Check if user exists
      const user = await storage.getUser(registrationData.userId);
      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      // Check if webinar exists
      const webinar = await storage.getWebinar(registrationData.webinarId);
      if (!webinar) {
        return res.status(404).json({ error: "Webinar not found" });
      }

      const registration = await storage.registerForWebinar(registrationData);
      res.status(201).json(registration);
    } catch (error) {
      res.status(400).json({ error: "Invalid registration data" });
    }
  });

  // Grant eBook access
  app.post("/api/ebook-access", async (req, res) => {
    try {
      const accessData = insertEbookAccessSchema.parse(req.body);
      
      // Check if user exists
      const user = await storage.getUser(accessData.userId);
      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      // Check if user already has access
      const existingAccess = await storage.getEbookAccess(accessData.userId);
      if (existingAccess) {
        return res.status(400).json({ error: "User already has eBook access" });
      }

      const access = await storage.grantEbookAccess(accessData);
      res.status(201).json(access);
    } catch (error) {
      res.status(400).json({ error: "Invalid access data" });
    }
  });

  // Create a subscription
  app.post("/api/subscriptions", async (req, res) => {
    try {
      const subscriptionData = insertSubscriptionSchema.parse(req.body);
      
      // Check if user exists
      const user = await storage.getUser(subscriptionData.userId);
      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      const subscription = await storage.createSubscription(subscriptionData);
      res.status(201).json(subscription);
    } catch (error) {
      res.status(400).json({ error: "Invalid subscription data" });
    }
  });

  // Get user's subscriptions
  app.get("/api/users/:userId/subscriptions", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const subscriptions = await storage.getUserSubscriptions(userId);
      res.json(subscriptions);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch subscriptions" });
    }
  });

  // Get user's webinar registrations
  app.get("/api/users/:userId/webinar-registrations", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const registrations = await storage.getUserWebinarRegistrations(userId);
      res.json(registrations);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch registrations" });
    }
  });

  // Get user's eBook access
  app.get("/api/users/:userId/ebook-access", async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const access = await storage.getEbookAccess(userId);
      if (!access) {
        return res.status(404).json({ error: "No eBook access found" });
      }
      res.json(access);
    } catch (error) {
      res.status(500).json({ error: "Failed to fetch eBook access" });
    }
  });

  const httpServer = createServer(app);

  return httpServer;
}
