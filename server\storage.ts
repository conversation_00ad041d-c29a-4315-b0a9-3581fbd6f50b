import { 
  users, webinars, subscriptions, webinarRegistrations, ebookAccess,
  type User, type InsertUser, type Webinar, type InsertWebinar,
  type Subscription, type InsertSubscription, type WebinarRegistration, 
  type InsertWebinarRegistration, type EbookAccess, type InsertEbookAccess
} from "@shared/schema";
import { db } from "./db";
import { eq } from "drizzle-orm";

export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Webinar operations
  getAllWebinars(): Promise<Webinar[]>;
  getWebinar(id: number): Promise<Webinar | undefined>;
  createWebinar(webinar: InsertWebinar): Promise<Webinar>;
  
  // Subscription operations
  getUserSubscriptions(userId: number): Promise<Subscription[]>;
  getActiveSubscription(userId: number): Promise<Subscription | undefined>;
  createSubscription(subscription: InsertSubscription): Promise<Subscription>;
  
  // Webinar registration operations
  registerForWebinar(registration: InsertWebinarRegistration): Promise<WebinarRegistration>;
  getUserWebinarRegistrations(userId: number): Promise<WebinarRegistration[]>;
  
  // eBook access operations
  getEbookAccess(userId: number): Promise<EbookAccess | undefined>;
  grantEbookAccess(access: InsertEbookAccess): Promise<EbookAccess>;
}

export class DatabaseStorage implements IStorage {
  // User operations
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  // Webinar operations
  async getAllWebinars(): Promise<Webinar[]> {
    return await db.select().from(webinars);
  }

  async getWebinar(id: number): Promise<Webinar | undefined> {
    const [webinar] = await db.select().from(webinars).where(eq(webinars.id, id));
    return webinar || undefined;
  }

  async createWebinar(insertWebinar: InsertWebinar): Promise<Webinar> {
    const [webinar] = await db
      .insert(webinars)
      .values(insertWebinar)
      .returning();
    return webinar;
  }

  // Subscription operations
  async getUserSubscriptions(userId: number): Promise<Subscription[]> {
    return await db.select().from(subscriptions).where(eq(subscriptions.userId, userId));
  }

  async getActiveSubscription(userId: number): Promise<Subscription | undefined> {
    const [subscription] = await db
      .select()
      .from(subscriptions)
      .where(eq(subscriptions.userId, userId));
    return subscription || undefined;
  }

  async createSubscription(insertSubscription: InsertSubscription): Promise<Subscription> {
    const [subscription] = await db
      .insert(subscriptions)
      .values(insertSubscription)
      .returning();
    return subscription;
  }

  // Webinar registration operations
  async registerForWebinar(insertRegistration: InsertWebinarRegistration): Promise<WebinarRegistration> {
    const [registration] = await db
      .insert(webinarRegistrations)
      .values(insertRegistration)
      .returning();
    return registration;
  }

  async getUserWebinarRegistrations(userId: number): Promise<WebinarRegistration[]> {
    return await db
      .select()
      .from(webinarRegistrations)
      .where(eq(webinarRegistrations.userId, userId));
  }

  // eBook access operations
  async getEbookAccess(userId: number): Promise<EbookAccess | undefined> {
    const [access] = await db
      .select()
      .from(ebookAccess)
      .where(eq(ebookAccess.userId, userId));
    return access || undefined;
  }

  async grantEbookAccess(insertAccess: InsertEbookAccess): Promise<EbookAccess> {
    const [access] = await db
      .insert(ebookAccess)
      .values(insertAccess)
      .returning();
    return access;
  }
}

// In-memory storage for demo purposes
class MemoryStorage implements IStorage {
  private users: User[] = [];
  private webinars: Webinar[] = [
    {
      id: 1,
      title: "AI-Powered Economic Forecasting",
      description: "Learn how machine learning algorithms are reshaping economic predictions and market analysis.",
      instructor: "Dr. Rajesh Kumar",
      instructorRole: "AI Economics Expert",
      date: new Date("2024-12-28T19:00:00.000Z"),
      duration: 120,
      price: "199.00",
      status: "upcoming",
      imageUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",
      avatarUrl: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100",
      viewCount: 0,
      createdAt: new Date(),
    },
    {
      id: 2,
      title: "Behavioral Economics & AI",
      description: "Explore how AI models can predict and analyze human economic behavior patterns.",
      instructor: "Prof. Anita Sharma",
      instructorRole: "Behavioral Economist",
      date: new Date("2025-01-05T18:30:00.000Z"),
      duration: 150,
      price: "199.00",
      status: "upcoming",
      imageUrl: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",
      avatarUrl: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100",
      viewCount: 0,
      createdAt: new Date(),
    },
    {
      id: 3,
      title: "Crypto Economics & AI Analysis",
      description: "Understand how AI tools are revolutionizing cryptocurrency market analysis and DeFi economics.",
      instructor: "Dr. Vikram Singh",
      instructorRole: "Crypto Economist",
      date: new Date("2024-12-15T18:00:00.000Z"),
      duration: 150,
      price: "199.00",
      status: "recorded",
      imageUrl: "https://images.unsplash.com/photo-1639762681485-074b7f938ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400",
      avatarUrl: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100",
      viewCount: 1247,
      createdAt: new Date(),
    }
  ];
  private subscriptions: Subscription[] = [];
  private webinarRegistrations: WebinarRegistration[] = [];
  private ebookAccesses: EbookAccess[] = [];
  private nextId = 4;

  async getUser(id: number): Promise<User | undefined> {
    return this.users.find(u => u.id === id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return this.users.find(u => u.username === username);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return this.users.find(u => u.email === email);
  }

  async createUser(user: InsertUser): Promise<User> {
    const newUser: User = {
      id: this.nextId++,
      ...user,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.users.push(newUser);
    return newUser;
  }

  async getAllWebinars(): Promise<Webinar[]> {
    return this.webinars;
  }

  async getWebinar(id: number): Promise<Webinar | undefined> {
    return this.webinars.find(w => w.id === id);
  }

  async createWebinar(webinar: InsertWebinar): Promise<Webinar> {
    const newWebinar: Webinar = {
      id: this.nextId++,
      ...webinar,
      viewCount: 0,
      createdAt: new Date(),
    };
    this.webinars.push(newWebinar);
    return newWebinar;
  }

  async getUserSubscriptions(userId: number): Promise<Subscription[]> {
    return this.subscriptions.filter(s => s.userId === userId);
  }

  async getActiveSubscription(userId: number): Promise<Subscription | undefined> {
    return this.subscriptions.find(s => s.userId === userId && s.status === 'active');
  }

  async createSubscription(subscription: InsertSubscription): Promise<Subscription> {
    const newSubscription: Subscription = {
      id: this.nextId++,
      ...subscription,
      createdAt: new Date(),
    };
    this.subscriptions.push(newSubscription);
    return newSubscription;
  }

  async registerForWebinar(registration: InsertWebinarRegistration): Promise<WebinarRegistration> {
    const newRegistration: WebinarRegistration = {
      id: this.nextId++,
      ...registration,
      registeredAt: new Date(),
      attended: false,
    };
    this.webinarRegistrations.push(newRegistration);
    return newRegistration;
  }

  async getUserWebinarRegistrations(userId: number): Promise<WebinarRegistration[]> {
    return this.webinarRegistrations.filter(r => r.userId === userId);
  }

  async getEbookAccess(userId: number): Promise<EbookAccess | undefined> {
    return this.ebookAccesses.find(e => e.userId === userId);
  }

  async grantEbookAccess(access: InsertEbookAccess): Promise<EbookAccess> {
    const newAccess: EbookAccess = {
      id: this.nextId++,
      ...access,
      purchasedAt: new Date(),
    };
    this.ebookAccesses.push(newAccess);
    return newAccess;
  }
}

// Use memory storage if no database is available, otherwise use database storage
export const storage = process.env.DATABASE_URL ? new DatabaseStorage() : new MemoryStorage();
