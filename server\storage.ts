import { 
  users, webinars, subscriptions, webinarRegistrations, ebookAccess,
  type User, type InsertUser, type Webinar, type InsertWebinar,
  type Subscription, type InsertSubscription, type WebinarRegistration, 
  type InsertWebinarRegistration, type EbookAccess, type InsertEbookAccess
} from "@shared/schema";
import { db } from "./db";
import { eq } from "drizzle-orm";

export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Webinar operations
  getAllWebinars(): Promise<Webinar[]>;
  getWebinar(id: number): Promise<Webinar | undefined>;
  createWebinar(webinar: InsertWebinar): Promise<Webinar>;
  
  // Subscription operations
  getUserSubscriptions(userId: number): Promise<Subscription[]>;
  getActiveSubscription(userId: number): Promise<Subscription | undefined>;
  createSubscription(subscription: InsertSubscription): Promise<Subscription>;
  
  // Webinar registration operations
  registerForWebinar(registration: InsertWebinarRegistration): Promise<WebinarRegistration>;
  getUserWebinarRegistrations(userId: number): Promise<WebinarRegistration[]>;
  
  // eBook access operations
  getEbookAccess(userId: number): Promise<EbookAccess | undefined>;
  grantEbookAccess(access: InsertEbookAccess): Promise<EbookAccess>;
}

export class DatabaseStorage implements IStorage {
  // User operations
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  // Webinar operations
  async getAllWebinars(): Promise<Webinar[]> {
    return await db.select().from(webinars);
  }

  async getWebinar(id: number): Promise<Webinar | undefined> {
    const [webinar] = await db.select().from(webinars).where(eq(webinars.id, id));
    return webinar || undefined;
  }

  async createWebinar(insertWebinar: InsertWebinar): Promise<Webinar> {
    const [webinar] = await db
      .insert(webinars)
      .values(insertWebinar)
      .returning();
    return webinar;
  }

  // Subscription operations
  async getUserSubscriptions(userId: number): Promise<Subscription[]> {
    return await db.select().from(subscriptions).where(eq(subscriptions.userId, userId));
  }

  async getActiveSubscription(userId: number): Promise<Subscription | undefined> {
    const [subscription] = await db
      .select()
      .from(subscriptions)
      .where(eq(subscriptions.userId, userId));
    return subscription || undefined;
  }

  async createSubscription(insertSubscription: InsertSubscription): Promise<Subscription> {
    const [subscription] = await db
      .insert(subscriptions)
      .values(insertSubscription)
      .returning();
    return subscription;
  }

  // Webinar registration operations
  async registerForWebinar(insertRegistration: InsertWebinarRegistration): Promise<WebinarRegistration> {
    const [registration] = await db
      .insert(webinarRegistrations)
      .values(insertRegistration)
      .returning();
    return registration;
  }

  async getUserWebinarRegistrations(userId: number): Promise<WebinarRegistration[]> {
    return await db
      .select()
      .from(webinarRegistrations)
      .where(eq(webinarRegistrations.userId, userId));
  }

  // eBook access operations
  async getEbookAccess(userId: number): Promise<EbookAccess | undefined> {
    const [access] = await db
      .select()
      .from(ebookAccess)
      .where(eq(ebookAccess.userId, userId));
    return access || undefined;
  }

  async grantEbookAccess(insertAccess: InsertEbookAccess): Promise<EbookAccess> {
    const [access] = await db
      .insert(ebookAccess)
      .values(insertAccess)
      .returning();
    return access;
  }
}

export const storage = new DatabaseStorage();
