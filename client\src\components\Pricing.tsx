import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Check, Shield, RotateCcw, Headphones } from "lucide-react";

const monthlyFeatures = [
  "Access to all live webinars",
  "Full eBook access", 
  "Community forum access",
  "Mobile app access",
  "24/7 email support"
];

const yearlyFeatures = [
  "Everything in Monthly",
  "Priority webinar booking",
  "Exclusive bonus content", 
  "1-on-1 mentor calls (2/month)",
  "Downloadable resources",
  "Certificate of completion"
];

const guarantees = [
  { icon: Shield, text: "Secure Payment" },
  { icon: RotateCcw, text: "30-day Refund" },
  { icon: Headphones, text: "24/7 Support" }
];

export default function Pricing() {
  return (
    <section id="pricing" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Choose Your Learning Path</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Flexible subscription plans designed to fit your learning goals and budget.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {/* Monthly Plan */}
          <Card className="bg-white rounded-3xl shadow-lg border-2 border-gray-100 hover:border-blue-200 p-8 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
            <CardContent className="p-0">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Monthly Plan</h3>
                <p className="text-gray-600 mb-6">Perfect for getting started</p>
                <div className="text-center">
                  <span className="text-5xl font-bold text-blue-600">₹99</span>
                  <span className="text-gray-500">/month</span>
                </div>
              </div>
              
              <div className="space-y-4 mb-8">
                {monthlyFeatures.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-emerald-500" />
                    <span>{feature}</span>
                  </div>
                ))}
              </div>
              
              <Button className="w-full gradient-primary text-white py-4 rounded-xl text-lg font-semibold hover:opacity-90 transition-all">
                Start Monthly Plan
              </Button>
            </CardContent>
          </Card>
          
          {/* Yearly Plan */}
          <Card className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl shadow-xl border-2 border-blue-200 p-8 relative hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <div className="gradient-emerald text-white px-6 py-2 rounded-full text-sm font-semibold">
                Most Popular - Save 60%
              </div>
            </div>
            
            <CardContent className="p-0 pt-4">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">Yearly Plan</h3>
                <p className="text-gray-600 mb-6">Best value for serious learners</p>
                <div className="text-center">
                  <span className="text-5xl font-bold text-blue-600">₹499</span>
                  <span className="text-gray-500">/year</span>
                </div>
                <div className="text-sm text-emerald-600 font-semibold mt-2">
                  Save ₹689 compared to monthly
                </div>
              </div>
              
              <div className="space-y-4 mb-8">
                {yearlyFeatures.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <Check className="w-5 h-5 text-emerald-500" />
                    <span className={index === 0 ? "font-semibold" : ""}>{feature}</span>
                  </div>
                ))}
              </div>
              
              <Button className="w-full gradient-emerald text-white py-4 rounded-xl text-lg font-semibold hover:opacity-90 transition-all shadow-lg">
                Start Yearly Plan
              </Button>
            </CardContent>
          </Card>
        </div>
        
        <div className="text-center mt-12">
          <p className="text-gray-600 mb-6">All plans include a 7-day free trial • Cancel anytime • No hidden fees</p>
          <div className="flex justify-center items-center gap-8 text-sm text-gray-500">
            {guarantees.map((item, index) => (
              <div key={index} className="flex items-center gap-2">
                <item.icon className="w-4 h-4 text-emerald-500" />
                <span>{item.text}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
