import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Check, Shield, RotateCcw, Headphones } from "lucide-react";

const monthlyFeatures = [
  "Access to all live webinars",
  "Full eBook access", 
  "Community forum access",
  "Mobile app access",
  "24/7 email support"
];

const yearlyFeatures = [
  "Everything in Monthly",
  "Priority webinar booking",
  "Exclusive bonus content", 
  "1-on-1 mentor calls (2/month)",
  "Downloadable resources",
  "Certificate of completion"
];

const guarantees = [
  { icon: Shield, text: "Secure Payment" },
  { icon: RotateCcw, text: "30-day Refund" },
  { icon: Headphones, text: "24/7 Support" }
];

export default function Pricing() {
  return (
    <section id="pricing" className="section-padding bg-slate-50">
      <div className="container-responsive">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-responsive-xl font-bold text-slate-900 mb-4">Choose Your Learning Path</h2>
          <p className="text-lg lg:text-xl text-slate-600 max-w-3xl mx-auto">
            Flexible subscription plans designed to fit your learning goals and budget.
          </p>
        </div>

        <div className="grid-responsive-pricing max-w-4xl mx-auto">
          {/* Monthly Plan */}
          <Card className="bg-white rounded-3xl shadow-lg border-2 border-slate-200 hover:border-primary-300 p-6 lg:p-8 card-hover">
            <CardContent className="p-0">
              <div className="text-center mb-6 lg:mb-8">
                <h3 className="text-xl lg:text-2xl font-bold text-slate-900 mb-2">Monthly Plan</h3>
                <p className="text-slate-600 mb-4 lg:mb-6 text-sm lg:text-base">Perfect for getting started</p>
                <div className="text-center">
                  <span className="text-3xl lg:text-5xl font-bold text-primary-600">₹99</span>
                  <span className="text-slate-500 text-sm lg:text-base">/month</span>
                </div>
              </div>

              <div className="space-y-3 lg:space-y-4 mb-6 lg:mb-8">
                {monthlyFeatures.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <Check className="w-4 lg:w-5 h-4 lg:h-5 text-emerald-500 flex-shrink-0" />
                    <span className="text-sm lg:text-base">{feature}</span>
                  </div>
                ))}
              </div>

              <Button className="w-full gradient-primary text-white py-3 lg:py-4 rounded-xl text-base lg:text-lg font-semibold hover:opacity-90 transition-all">
                Start Monthly Plan
              </Button>
            </CardContent>
          </Card>
          
          {/* Yearly Plan */}
          <Card className="gradient-subtle rounded-3xl shadow-xl border-2 border-primary-200 p-6 lg:p-8 relative card-hover">
            <div className="absolute -top-3 lg:-top-4 left-1/2 transform -translate-x-1/2">
              <div className="gradient-emerald text-white px-4 lg:px-6 py-1.5 lg:py-2 rounded-full text-xs lg:text-sm font-semibold">
                Most Popular - Save 60%
              </div>
            </div>

            <CardContent className="p-0 pt-3 lg:pt-4">
              <div className="text-center mb-6 lg:mb-8">
                <h3 className="text-xl lg:text-2xl font-bold text-slate-900 mb-2">Yearly Plan</h3>
                <p className="text-slate-600 mb-4 lg:mb-6 text-sm lg:text-base">Best value for serious learners</p>
                <div className="text-center">
                  <span className="text-3xl lg:text-5xl font-bold text-primary-600">₹499</span>
                  <span className="text-slate-500 text-sm lg:text-base">/year</span>
                </div>
                <div className="text-xs lg:text-sm text-emerald-600 font-semibold mt-2">
                  Save ₹689 compared to monthly
                </div>
              </div>

              <div className="space-y-3 lg:space-y-4 mb-6 lg:mb-8">
                {yearlyFeatures.map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <Check className="w-4 lg:w-5 h-4 lg:h-5 text-emerald-500 flex-shrink-0" />
                    <span className={`text-sm lg:text-base ${index === 0 ? "font-semibold" : ""}`}>{feature}</span>
                  </div>
                ))}
              </div>

              <Button className="w-full gradient-emerald text-white py-3 lg:py-4 rounded-xl text-base lg:text-lg font-semibold hover:opacity-90 transition-all shadow-lg">
                Start Yearly Plan
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="text-center mt-8 lg:mt-12">
          <p className="text-slate-600 mb-4 lg:mb-6 text-sm lg:text-base">All plans include a 7-day free trial • Cancel anytime • No hidden fees</p>
          <div className="flex flex-col sm:flex-row justify-center items-center gap-4 sm:gap-6 lg:gap-8 text-xs sm:text-sm text-slate-500">
            {guarantees.map((item, index) => (
              <div key={index} className="flex items-center gap-2">
                <item.icon className="w-4 h-4 text-emerald-500 flex-shrink-0" />
                <span>{item.text}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
