import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Calendar, Clock, User, Eye, Play } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import type { Webinar } from "@shared/schema";

function getStatusDisplay(status: string) {
  switch (status.toLowerCase()) {
    case "upcoming":
      return { label: "UPCOMING", color: "bg-blue-100 text-blue-600" };
    case "live":
      return { label: "LIVE", color: "bg-red-100 text-red-600" };
    case "recorded":
      return { label: "RECORDED", color: "bg-green-100 text-green-600" };
    default:
      return { label: status.toUpperCase(), color: "bg-gray-100 text-gray-600" };
  }
}

function formatDate(dateString: string | Date) {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric', 
    year: 'numeric' 
  });
}

function formatTime(dateString: string | Date) {
  const date = new Date(dateString);
  return date.toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit',
    hour12: true,
    timeZone: 'Asia/Kolkata'
  }) + ' IST';
}

function formatDuration(minutes: number) {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  if (hours > 0) {
    return `${hours}h ${mins > 0 ? mins + 'm' : ''} Duration`.trim();
  }
  return `${mins}m Duration`;
}

export default function Webinars() {
  const { data: webinars, isLoading, error } = useQuery<Webinar[]>({
    queryKey: ['/api/webinars'],
  });

  if (isLoading) {
    return (
      <section id="webinars" className="section-padding bg-white">
        <div className="container-responsive">
          <div className="text-center mb-12 lg:mb-16">
            <h2 className="text-responsive-xl font-bold text-slate-900 mb-4">Live Expert Webinars</h2>
            <p className="text-lg lg:text-xl text-slate-600 max-w-3xl mx-auto">
              Join our renowned economists and AI specialists for interactive sessions that bridge theory with real-world applications.
            </p>
          </div>
          <div className="grid-responsive-cards">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="bg-slate-300 h-48 rounded-t-2xl"></div>
                <div className="bg-white p-6 rounded-b-2xl shadow-lg">
                  <div className="h-4 bg-slate-300 rounded mb-4"></div>
                  <div className="h-6 bg-slate-300 rounded mb-2"></div>
                  <div className="h-4 bg-slate-300 rounded mb-4"></div>
                  <div className="h-10 bg-slate-300 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section id="webinars" className="section-padding bg-white">
        <div className="container-responsive">
          <div className="text-center">
            <h2 className="text-responsive-xl font-bold text-slate-900 mb-4">Live Expert Webinars</h2>
            <p className="text-lg lg:text-xl text-red-600">Failed to load webinars. Please try again later.</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="webinars" className="section-padding bg-white">
      <div className="container-responsive">
        <div className="text-center mb-12 lg:mb-16">
          <h2 className="text-responsive-xl font-bold text-slate-900 mb-4">Live Expert Webinars</h2>
          <p className="text-lg lg:text-xl text-slate-600 max-w-3xl mx-auto">
            Join our renowned economists and AI specialists for interactive sessions that bridge theory with real-world applications.
          </p>
        </div>

        <div className="grid-responsive-cards">
          {webinars?.map((webinar) => {
            const statusDisplay = getStatusDisplay(webinar.status);
            const isRecorded = webinar.status.toLowerCase() === 'recorded';
            
            return (
              <Card
                key={webinar.id}
                className="bg-white rounded-2xl shadow-lg card-hover overflow-hidden group"
              >
                <div className="relative">
                  <img
                    src={webinar.imageUrl || "https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400"}
                    alt={webinar.title}
                    className="w-full h-48 object-cover"
                  />
                </div>
                <CardContent className="p-4 sm:p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className={`${statusDisplay.color} px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium`}>
                      {statusDisplay.label}
                    </span>
                    <span className="text-xl sm:text-2xl font-bold text-primary-600">₹{webinar.price}</span>
                  </div>

                  <h3 className="text-lg sm:text-xl font-semibold text-slate-900 mb-2 line-clamp-2">{webinar.title}</h3>
                  <p className="text-slate-600 mb-4 text-sm sm:text-base line-clamp-3">{webinar.description}</p>

                  <div className="flex items-center text-xs sm:text-sm text-slate-500 mb-4 flex-wrap gap-2">
                    {isRecorded ? (
                      <>
                        <div className="flex items-center">
                          <Play className="w-4 h-4 mr-1" />
                          <span>{formatDuration(webinar.duration)}</span>
                        </div>
                        <div className="flex items-center">
                          <Eye className="w-4 h-4 mr-1" />
                          <span>{webinar.viewCount || 0} views</span>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          <span className="hidden sm:inline">{formatDate(webinar.date)}</span>
                          <span className="sm:hidden">{formatDate(webinar.date).split(',')[0]}</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          <span>{formatTime(webinar.date)}</span>
                        </div>
                      </>
                    )}
                  </div>

                  <div className="flex items-center mb-4">
                    <img
                      src={webinar.avatarUrl || "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100"}
                      alt={webinar.instructor}
                      className="w-8 sm:w-10 h-8 sm:h-10 rounded-full mr-3 object-cover flex-shrink-0"
                    />
                    <div className="min-w-0">
                      <div className="font-medium text-slate-900 text-sm sm:text-base truncate">{webinar.instructor}</div>
                      <div className="text-xs sm:text-sm text-slate-500 truncate">{webinar.instructorRole}</div>
                    </div>
                  </div>

                  <Button className="w-full gradient-primary text-white py-2.5 sm:py-3 rounded-xl font-semibold hover:opacity-90 transition-all text-sm sm:text-base">
                    {isRecorded ? "Watch Now" :
                     webinar.status.toLowerCase() === "live" ? "Book Now" : "Register"}
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
}
