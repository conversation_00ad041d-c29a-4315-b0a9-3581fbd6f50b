Lovable
Worked for 3m 59s
4m ago

Thought for 5 seconds
I'll create Alnomics - a sophisticated AI and Economics learning platform. This evokes modern EdTech platforms like Coursera and Udemy, but with a focus on the intersection of AI and Economics. I'll create a professional, tech-forward design with clean lines and gradients.

First Version Features:
Modern landing page with hero section
Course/webinar showcase
Pricing plans display
eBook preview section
Navigation and footer
Responsive design with professional color scheme
Design Approach:
Colors: Deep blues and purples with accent greens (suggesting technology and growth)
Typography: Clean, modern fonts
Gradients: Subtle tech-inspired gradients
Animations: Smooth transitions and hover effects
I'll create Alnomics - a sophisticated AI and Economics learning platform. This evokes modern EdTech platforms like Coursera and Udemy, but with a focus on the intersection of AI and Economics. I'll create a professional, tech-forward design with clean lines and gradients.

First Version Features:
Modern landing page with hero section
Course/webinar showcase
Pricing plans display
eBook preview section
Navigation and footer
Responsive design with professional color scheme
Design Approach:
Colors: Deep blues and purples with accent greens (suggesting technology and growth)
Typography: Clean, modern fonts
Gradients: Subtle tech-inspired gradients
Animations: Smooth transitions and hover effects
Show all (13)
Edited
src/index.css
Edited
tailwind.config.ts
Edited
src/components/ui/button.tsx
Generated image
A sleek hero banner image for Alnomics, an AI and Economics learning platform. 16:9 aspect ratio. Modern, abstract illustration featuring interconnected neural network nodes flowing into economic graphs and charts. Deep blue and purple gradients with bright accent elements. Professional, tech-forward aesthetic with floating geometric shapes representing data points and economic indicators. Ultra high resolution.
Edited
src/components/Header.tsx
Edited
src/components/Hero.tsx
Edited
src/components/Webinars.tsx
Edited
src/components/EBook.tsx
Edited
src/components/Pricing.tsx
Edited
src/components/Features.tsx
Edited
src/components/Footer.tsx
Edited
src/pages/Index.tsx
Edited
index.html
I've created Alnomics - a sophisticated AI and Economics learning platform! Here's what I built:

✨ Features Implemented:
Professional Design System with tech-forward blue/purple gradients and smooth animations
Hero Section with AI-generated banner showcasing the platform's focus
Live Webinars Section featuring ₹199 expert-led sessions
Protected eBook (₹99) with secure, non-downloadable access
Subscription Plans (Monthly ₹99, Yearly ₹499) with clear value propositions
Additional Features showcase including tools, community, and learning tracks
Responsive Design that works beautifully on all devices
🎨 Design Highlights:
Modern gradient backgrounds and smooth hover effects
Beautiful card layouts with elegant shadows
Professional color scheme suggesting technology and growth
Consistent design tokens throughout the platform
Hero and accent button variants for key actions
The platform is ready for users to explore webinars, access the eBook, and choose subscription plans. All pricing is clearly displayed in Indian Rupees as requested!