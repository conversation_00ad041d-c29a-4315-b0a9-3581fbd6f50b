import { Pool, neonConfig } from '@neondatabase/serverless';
import { drizzle } from 'drizzle-orm/neon-serverless';
import ws from "ws";
import * as schema from "@shared/schema";

neonConfig.webSocketConstructor = ws;

// For demo purposes, we'll create a mock database if DATABASE_URL is not set
let db: any;
let pool: any;

if (!process.env.DATABASE_URL) {
  console.warn("DATABASE_URL not set. Using mock database for demo purposes.");
  // Create a mock database object for demo
  db = {
    select: () => ({
      from: () => ({
        where: () => Promise.resolve([]),
      }),
    }),
    insert: () => ({
      values: () => ({
        returning: () => Promise.resolve([]),
      }),
    }),
    delete: () => Promise.resolve(),
  } as any;
  pool = null;
} else {
  pool = new Pool({ connectionString: process.env.DATABASE_URL });
  db = drizzle({ client: pool, schema });
}

export { db, pool };